import json

import numpy as np
import pandas as pd
from datetime import datetime
import time
import multiprocessing as mp
from tqdm import tqdm



def dataload(kline_path):
    # 读取 JSON 文件
    with open(kline_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 转换为 DataFrame
    columns = [
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_volume', 'trades',
        'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
    ]
    df = pd.DataFrame(data, columns=columns)

    # 将时间戳转换为可读时间
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')

    return df


def compress_consecutive(arr):
    arr = np.asarray(arr)
    if len(arr) == 0:
        return arr
    # 删除为 0 的元素
    arr = arr[arr != 0]
    # 找出变化的位置
    change_points = np.where(np.diff(arr) != 0)[0] + 1
    # 第一个元素一定要保留
    compressed = np.concatenate(([arr[0]], arr[change_points]))
    return compressed


def evaluate(interval):
    split_rate = 15
    # 第一步：创建一个形状相同的新数组
    labels = np.zeros_like(interval)

    # 第二步：计算分位数
    lower_threshold = np.percentile(interval, split_rate)  # 最小10%
    upper_threshold = np.percentile(interval, 100 - split_rate)  # 最大10%

    # 第三步：根据阈值进行标记
    labels[interval > upper_threshold] = 1
    labels[interval < lower_threshold] = -1
    # 其余保持为0
    pass

    # 一次转换记为一分
    compressed = compress_consecutive(labels)

    return len(compressed) - 1




def _compute_row(args):
    """Helper function for multiprocessing"""
    left, close, n, min_length = args
    row = np.full(n, np.nan)
    for right in range(left + min_length, n):
        row[right] = evaluate(close[left:right + 1])
    return left, row

def box_score(close, result_path):
    n = len(close)
    min_length = 20
    scores = np.zeros(shape=(n, n))

    # Prepare arguments for multiprocessing
    args = [(left, close, n, min_length) for left in range(n)]

    # Use multiprocessing Pool
    with mp.Pool() as pool:
        # results = pool.map(_compute_row, args)
        results = list(tqdm(pool.imap_unordered(_compute_row, args), total=len(args)))

    # Collect results
    for left, row in results:
        scores[left] = row

    # 可选：把下三角设为 NaN 更清晰
    scores = np.triu(scores, k=1)  # 只保留上三角（不含对角线），下三角为0
    scores[scores == 0] = np.nan  # 用 NaN 替代未计算部分（可选）

    # 保存
    df = pd.DataFrame(scores)
    if result_path.endswith('.csv'):
        df.to_csv(result_path, index=False, header=False)
    elif result_path.endswith('.xlsx'):
        df.to_excel(result_path, index=False, header=False)
    else:
        raise ValueError(f"Unsupported file extension: {result_path}")
    return df


if __name__ == '__main__':
    start_time = time.time()
    
    target = 'BTCUSDT_4h'
    kline_path = f'./data/{target}.json'
    result_path = f'./results/{target}.csv'
    data = dataload(kline_path)
    close = data['close'].astype(float).to_numpy()
    box_score(close, result_path)
    end_time = time.time()
    print(f"程序运行时间：{end_time - start_time}")