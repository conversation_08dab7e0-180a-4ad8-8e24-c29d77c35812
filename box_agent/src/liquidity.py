"""
Liquidity filtering module.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any


def check_liquidity(
    df: pd.DataFrame,
    min_liquidity_usd: float,
    timeframe: str = "4h"
) -> pd.Series:
    """
    Check if liquidity requirements are met for each bar.
    
    Args:
        df: DataFrame with K-line data (requires 'close' and 'volume' columns)
        min_liquidity_usd: Minimum liquidity in USD
        timeframe: Timeframe of the data (for annualization)
        
    Returns:
        Boolean Series indicating whether liquidity requirements are met
    """
    # Calculate notional volume (price * volume)
    notional_volume = df['close'] * df['volume']
    
    # Check if notional volume meets minimum requirement
    liquidity_ok = notional_volume >= min_liquidity_usd
    
    return liquidity_ok


def calculate_rolling_liquidity(
    df: pd.DataFrame,
    window: int = 30  # Default to 30 periods (e.g., 30 4h bars = 5 days)
) -> pd.Series:
    """
    Calculate rolling average liquidity.
    
    Args:
        df: DataFrame with K-line data (requires 'close' and 'volume' columns)
        window: Rolling window size
        
    Returns:
        Series with rolling average liquidity in USD
    """
    # Calculate notional volume (price * volume)
    notional_volume = df['close'] * df['volume']
    
    # Calculate rolling average
    rolling_liquidity = notional_volume.rolling(window=window).mean()
    
    return rolling_liquidity


def filter_by_liquidity(
    df: pd.DataFrame,
    min_liquidity_usd: float,
    window: int = 30
) -> pd.DataFrame:
    """
    Filter DataFrame to only include periods with sufficient liquidity.
    
    Args:
        df: DataFrame with K-line data
        min_liquidity_usd: Minimum liquidity in USD
        window: Rolling window size for liquidity calculation
        
    Returns:
        Filtered DataFrame
    """
    # Calculate rolling liquidity
    rolling_liquidity = calculate_rolling_liquidity(df, window)
    
    # Create filter
    liquidity_filter = rolling_liquidity >= min_liquidity_usd
    
    # Apply filter
    filtered_df = df[liquidity_filter]
    
    return filtered_df


def compute_liquidity_score(
    df: pd.DataFrame,
    min_liquidity_usd: float,
    window: int = 30
) -> pd.Series:
    """
    Compute a liquidity score (0-1) based on how well liquidity requirements are met.
    
    Args:
        df: DataFrame with K-line data
        min_liquidity_usd: Minimum liquidity in USD
        window: Rolling window size for liquidity calculation
        
    Returns:
        Series with liquidity scores (1.0 = meets requirements, <1.0 = below requirements)
    """
    # Calculate rolling liquidity
    rolling_liquidity = calculate_rolling_liquidity(df, window)
    
    # Compute score
    # If liquidity >= min_liquidity_usd, score = 1.0
    # If liquidity < min_liquidity_usd, score = liquidity / min_liquidity_usd
    liquidity_score = np.minimum(rolling_liquidity / min_liquidity_usd, 1.0)
    
    # Fill NaN values (from rolling window) with 0
    liquidity_score = liquidity_score.fillna(0.0)
    
    return liquidity_score


# Example usage (for testing)
if __name__ == "__main__":
    print("Liquidity module loaded successfully.")
    print("Functions available:")
    print("- check_liquidity(df, min_liquidity_usd, timeframe)")
    print("- calculate_rolling_liquidity(df, window)")
    print("- filter_by_liquidity(df, min_liquidity_usd, window)")
    print("- compute_liquidity_score(df, min_liquidity_usd, window)")