"""
Online quantile algorithms for box boundary detection.
Supports both sliding window and t-digest methods.
"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional


def rolling_box_bounds(
    prices: pd.Series, 
    window: int, 
    q_lo: float = 0.1, 
    q_hi: float = 0.9, 
    method: str = "sliding_window"
) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
    """
    Calculate rolling box bounds (Qlo, Qhi, mid, bandwidth) for price series.
    
    Args:
        prices: Price series (e.g., close prices)
        window: Rolling window size (in bars)
        q_lo: Lower quantile (e.g., 0.1 for 10%)
        q_hi: Upper quantile (e.g., 0.9 for 90%)
        method: Method to use ("sliding_window" or "tdigest")
        
    Returns:
        Tuple of (Qlo, Qhi, mid, bandwidth) as pandas Series
        First (window-1) values will be NaN
    """
    if method == "sliding_window":
        return _rolling_box_bounds_sliding_window(prices, window, q_lo, q_hi)
    elif method == "tdigest":
        return _rolling_box_bounds_tdigest(prices, window, q_lo, q_hi)
    else:
        raise ValueError(f"Unknown method: {method}")


def _rolling_box_bounds_sliding_window(
    prices: pd.Series, 
    window: int, 
    q_lo: float, 
    q_hi: float
) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
    """
    Calculate rolling box bounds using sliding window method.
    
    Args:
        prices: Price series
        window: Rolling window size
        q_lo: Lower quantile
        q_hi: Upper quantile
        
    Returns:
        Tuple of (Qlo, Qhi, mid, bandwidth) as pandas Series
    """
    # Initialize result series with NaN
    qlo = pd.Series(np.nan, index=prices.index, dtype=float)
    qhi = pd.Series(np.nan, index=prices.index, dtype=float)
    
    # Calculate rolling quantiles
    qlo.iloc[window-1:] = prices.rolling(window=window).quantile(q_lo)
    qhi.iloc[window-1:] = prices.rolling(window=window).quantile(q_hi)
    
    # Calculate mid and bandwidth
    mid = (qlo + qhi) / 2
    bandwidth = qhi - qlo
    
    return qlo, qhi, mid, bandwidth


def _rolling_box_bounds_tdigest(
    prices: pd.Series, 
    window: int, 
    q_lo: float, 
    q_hi: float
) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
    """
    Calculate rolling box bounds using t-digest method.
    Note: This is a simplified implementation. A full implementation would
    require a proper t-digest library.
    
    Args:
        prices: Price series
        window: Rolling window size
        q_lo: Lower quantile
        q_hi: Upper quantile
        
    Returns:
        Tuple of (Qlo, Qhi, mid, bandwidth) as pandas Series
    """
    try:
        from tdigest import TDigest
    except ImportError:
        raise ImportError("tdigest library is required for t-digest method. "
                          "Install it with 'pip install tdigest' or use "
                          "method='sliding_window' instead.")
    
    # Initialize result series with NaN
    qlo = pd.Series(np.nan, index=prices.index, dtype=float)
    qhi = pd.Series(np.nan, index=prices.index, dtype=float)
    
    # Calculate rolling quantiles using t-digest
    for i in range(window-1, len(prices)):
        # Create a digest for the window
        digest = TDigest()
        # Add values in the window to the digest
        for j in range(i - window + 1, i + 1):
            digest.update(prices.iloc[j])
        
        # Calculate quantiles
        qlo.iloc[i] = digest.quantile(q_lo)
        qhi.iloc[i] = digest.quantile(q_hi)
    
    # Calculate mid and bandwidth
    mid = (qlo + qhi) / 2
    bandwidth = qhi - qlo
    
    return qlo, qhi, mid, bandwidth


# Example usage (for testing)
if __name__ == "__main__":
    # Generate sample data
    np.random.seed(42)
    n = 1000
    prices = pd.Series(
        100 + np.cumsum(np.random.randn(n) * 0.1), 
        name="close"
    )
    
    # Test sliding window method
    qlo, qhi, mid, bw = rolling_box_bounds(
        prices, 
        window=100, 
        q_lo=0.1, 
        q_hi=0.9, 
        method="sliding_window"
    )
    
    print("Sliding window method:")
    print(f"Qlo: {qlo.tail()}")
    print(f"Qhi: {qhi.tail()}")
    print(f"Mid: {mid.tail()}")
    print(f"Bandwidth: {bw.tail()}")
    
    # Test t-digest method (if available)
    try:
        qlo, qhi, mid, bw = rolling_box_bounds(
            prices, 
            window=100, 
            q_lo=0.1, 
            q_hi=0.9, 
            method="tdigest"
        )
        
        print("\nTDigest method:")
        print(f"Qlo: {qlo.tail()}")
        print(f"Qhi: {qhi.tail()}")
        print(f"Mid: {mid.tail()}")
        print(f"Bandwidth: {bw.tail()}")
    except ImportError as e:
        print(f"\nTDigest method not available: {e}")