"""
Data loading, validation, alignment, and resampling for K-line data.
"""

import pandas as pd
import numpy as np
import os
from typing import List, Optional, Tuple
import json


def load_kline_data(
    file_path: str, 
    timeframe: str = "4h"
) -> pd.DataFrame:
    """
    Load K-line data from JSON file and convert to pandas DataFrame.
    
    Args:
        file_path: Path to the JSON file containing K-line data
        timeframe: Timeframe of the data (for validation)
        
    Returns:
        DataFrame with columns: timestamp, open, high, low, close, volume,
        close_time, quote_asset_volume, number_of_trades, 
        taker_buy_base_asset_volume, taker_buy_quote_asset_volume
    """
    # Read JSON data with UTF-8 encoding
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Convert to DataFrame
    df = pd.DataFrame(data, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
    ])
    
    # Convert data types
    numeric_columns = ['open', 'high', 'low', 'close', 'volume',
                      'quote_asset_volume', 'taker_buy_base_asset_volume',
                      'taker_buy_quote_asset_volume']
    df[numeric_columns] = df[numeric_columns].apply(pd.to_numeric)
    
    # Convert timestamp to datetime
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
    df['close_time'] = pd.to_datetime(df['close_time'], unit='ms', utc=True)
    
    # Set timestamp as index
    df.set_index('timestamp', inplace=True)
    
    # Sort by timestamp
    df.sort_index(inplace=True)
    
    # Validate data
    df = validate_ohlc_data(df)
    
    return df


def validate_ohlc_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Validate OHLC data for common issues.
    
    Args:
        df: DataFrame with OHLC data
        
    Returns:
        Validated DataFrame
    """
    # Check for missing values
    if df.isnull().any().any():
        print("Warning: Missing values found in data. Forward filling.")
        df.fillna(method='ffill', inplace=True)
    
    # Check for duplicate timestamps
    if df.index.duplicated().any():
        print("Warning: Duplicate timestamps found. Keeping first occurrence.")
        df = df[~df.index.duplicated(keep='first')]
    
    # Check for invalid OHLC relationships
    invalid_ohlc = (
        (df['high'] < df['low']) |
        (df['open'] > df['high']) |
        (df['open'] < df['low']) |
        (df['close'] > df['high']) |
        (df['close'] < df['low'])
    )
    
    if invalid_ohlc.any():
        print(f"Warning: Invalid OHLC relationships found in {invalid_ohlc.sum()} rows. Removing those rows.")
        df = df[~invalid_ohlc]
    
    return df


def resample_kline_data(
    df: pd.DataFrame, 
    new_timeframe: str
) -> pd.DataFrame:
    """
    Resample K-line data to a new timeframe.
    
    Args:
        df: DataFrame with K-line data
        new_timeframe: New timeframe (e.g., '1h', '4h', '12h')
        
    Returns:
        Resampled DataFrame
    """
    # Define aggregation rules
    agg_rules = {
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum',
        'quote_asset_volume': 'sum',
        'number_of_trades': 'sum',
        'taker_buy_base_asset_volume': 'sum',
        'taker_buy_quote_asset_volume': 'sum'
    }
    
    # Resample
    resampled = df.resample(new_timeframe).agg(agg_rules)
    
    # Remove rows with NaN values (which occur when there's no data in a resampling period)
    resampled.dropna(inplace=True)
    
    return resampled


def align_symbols_data(
    data_dict: dict
) -> pd.DataFrame:
    """
    Align multiple symbols' data on the same timestamp index.
    
    Args:
        data_dict: Dictionary with symbol names as keys and DataFrames as values
        
    Returns:
        Multi-index DataFrame with all symbols aligned
    """
    # Use pd.concat with keys to create a multi-index DataFrame
    aligned_data = pd.concat(data_dict, axis=1, keys=data_dict.keys())
    
    # Forward fill missing values
    aligned_data.fillna(method='ffill', inplace=True)
    
    return aligned_data


# Example usage (for testing)
if __name__ == "__main__":
    # This would be used if we had actual data files to load
    # For now, we'll just show the function signatures
    print("Data loader module loaded successfully.")
    print("Functions available:")
    print("- load_kline_data(file_path, timeframe)")
    print("- validate_ohlc_data(df)")
    print("- resample_kline_data(df, new_timeframe)")
    print("- align_symbols_data(data_dict)")