"""
Cost models for fee, slippage, and funding rate estimation.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any


def estimate_transaction_cost(
    price: float,
    quantity: float,
    taker_fee_bps: float,
    slippage_bps: float
) -> float:
    """
    Estimate transaction cost for a trade.
    
    Args:
        price: Asset price
        quantity: Trade quantity
        taker_fee_bps: Taker fee in basis points
        slippage_bps: Slippage in basis points
        
    Returns:
        Estimated transaction cost in quote currency
    """
    # Base transaction value
    value = price * quantity
    
    # Fee component
    fee = value * (taker_fee_bps / 10000)
    
    # Slippage component
    slippage = value * (slippage_bps / 10000)
    
    # Total cost
    total_cost = fee + slippage
    
    return total_cost


def estimate_funding_rate_cost(
    position_value: float,
    funding_rate_bps_per_day: float,
    holding_days: float
) -> float:
    """
    Estimate funding rate cost for a position.
    
    Args:
        position_value: Position value in quote currency
        funding_rate_bps_per_day: Funding rate in basis points per day
        holding_days: Number of days holding the position
        
    Returns:
        Estimated funding cost in quote currency
    """
    # Funding cost
    funding_cost = (
        position_value * 
        (funding_rate_bps_per_day / 10000) * 
        holding_days
    )
    
    return funding_cost


def compute_min_viable_edge(
    price: float,
    taker_fee_bps: float,
    slippage_bps: float,
    min_edge_return_bps: float
) -> float:
    """
    Compute minimum viable edge (price movement) to cover costs.
    
    Args:
        price: Asset price
        taker_fee_bps: Taker fee in basis points
        slippage_bps: Slippage in basis points
        min_edge_return_bps: Minimum edge return in basis points
        
    Returns:
        Minimum viable edge in price units
    """
    # Total cost in basis points (round trip: entry + exit)
    total_cost_bps = (taker_fee_bps + slippage_bps) * 2
    
    # Minimum required return in basis points
    min_return_bps = total_cost_bps + min_edge_return_bps
    
    # Convert to price units
    min_edge = price * (min_return_bps / 10000)
    
    return min_edge


def compute_cost_risk(
    volatility: float,
    price: float,
    taker_fee_bps: float,
    slippage_bps: float,
    min_edge_return_bps: float
) -> float:
    """
    Compute cost risk measure.
    
    Args:
        volatility: Price volatility (standard deviation of returns)
        price: Asset price
        taker_fee_bps: Taker fee in basis points
        slippage_bps: Slippage in basis points
        min_edge_return_bps: Minimum edge return in basis points
        
    Returns:
        Cost risk measure (higher is worse)
    """
    # Minimum viable edge
    min_edge = compute_min_viable_edge(
        price, taker_fee_bps, slippage_bps, min_edge_return_bps
    )
    
    # If volatility is too low compared to min edge, it's risky
    if volatility > 0:
        cost_risk = min_edge / (volatility * price)
    else:
        cost_risk = np.inf  # Infinite risk if no volatility
        
    return cost_risk


# Example usage (for testing)
if __name__ == "__main__":
    print("Cost models module loaded successfully.")
    print("Functions available:")
    print("- estimate_transaction_cost(price, quantity, taker_fee_bps, slippage_bps)")
    print("- estimate_funding_rate_cost(position_value, funding_rate_bps_per_day, holding_days)")
    print("- compute_min_viable_edge(price, taker_fee_bps, slippage_bps, min_edge_return_bps)")
    print("- compute_cost_risk(volatility, price, taker_fee_bps, slippage_bps, min_edge_return_bps)")