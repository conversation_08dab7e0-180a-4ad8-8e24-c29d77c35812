"""
Visualization module for box pattern analysis.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Tuple
import warnings


def plot_box_bounds(
    df: pd.DataFrame,
    qlo: pd.Series,
    qhi: pd.Series,
    mid: pd.Series,
    title: str = "Box Boundaries"
) -> plt.Figure:
    """
    Plot price series with box boundaries.
    
    Args:
        df: DataFrame with K-line data (requires 'close' column)
        qlo: Lower quantile series
        qhi: Upper quantile series
        mid: Midline series
        title: Plot title
        
    Returns:
        Matplotlib figure
    """
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Plot close prices
    ax.plot(df.index, df['close'], label='Close Price', color='black', linewidth=1)
    
    # Plot box boundaries
    ax.plot(qlo.index, qlo, label='Lower Bound (Qlo)', color='blue', linewidth=1, linestyle='--')
    ax.plot(qhi.index, qhi, label='Upper Bound (Qhi)', color='red', linewidth=1, linestyle='--')
    ax.plot(mid.index, mid, label='Midline', color='green', linewidth=1, linestyle='-.')
    
    # Fill area between bounds
    ax.fill_between(qlo.index, qlo, qhi, color='gray', alpha=0.2, label='Box Area')
    
    # Formatting
    ax.set_title(title)
    ax.set_xlabel('Time')
    ax.set_ylabel('Price')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Rotate x-axis labels for better readability
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
    
    return fig


def plot_discretized_series(
    df: pd.DataFrame,
    states: pd.Series,
    title: str = "Discretized Price Series"
) -> plt.Figure:
    """
    Plot discretized price series.
    
    Args:
        df: DataFrame with K-line data (requires 'close' column)
        states: Discretized states series
        title: Plot title
        
    Returns:
        Matplotlib figure
    """
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Plot close prices
    ax.plot(df.index, df['close'], label='Close Price', color='black', linewidth=1)
    
    # Plot state changes
    # Color code: Low=red, Mid=yellow, High=green
    colors = states.map({'Low': 'red', 'Mid': 'yellow', 'High': 'green'})
    
    # Plot scatter points for states
    for state in ['Low', 'Mid', 'High']:
        mask = states == state
        if mask.any():
            ax.scatter(
                df.index[mask], 
                df['close'][mask], 
                c=colors[mask], 
                s=20, 
                alpha=0.7, 
                label=state,
                zorder=3
            )
    
    # Formatting
    ax.set_title(title)
    ax.set_xlabel('Time')
    ax.set_ylabel('Price')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Rotate x-axis labels for better readability
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
    
    return fig


def plot_roundtrips(
    df: pd.DataFrame,
    states: pd.Series,
    trip_stats: List[Any],  # Assuming TripStats from box_scanner
    title: str = "Roundtrips"
) -> plt.Figure:
    """
    Plot roundtrips on price series.
    
    Args:
        df: DataFrame with K-line data (requires 'close' column)
        states: Discretized states series
        trip_stats: List of trip statistics
        title: Plot title
        
    Returns:
        Matplotlib figure
    """
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Plot close prices
    ax.plot(df.index, df['close'], label='Close Price', color='black', linewidth=1)
    
    # Plot roundtrips
    for i, trip in enumerate(trip_stats):
        # Plot entry and exit points
        ax.scatter(
            trip.start_time, 
            trip.start_price, 
            c='blue', 
            s=50, 
            marker='^',
            label='Entry' if i == 0 else "",
            zorder=4
        )
        ax.scatter(
            trip.end_time, 
            trip.end_price, 
            c='red', 
            s=50, 
            marker='v',
            label='Exit' if i == 0 else "",
            zorder=4
        )
        
        # Draw line connecting entry and exit
        ax.plot(
            [trip.start_time, trip.end_time],
            [trip.start_price, trip.end_price],
            color='orange',
            linewidth=1,
            alpha=0.7,
            zorder=2
        )
    
    # Formatting
    ax.set_title(title)
    ax.set_xlabel('Time')
    ax.set_ylabel('Price')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Rotate x-axis labels for better readability
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
    
    return fig


def plot_score_contributions(
    penalties: Dict[str, float],
    weights: Dict[str, float],
    X_star: int,
    title: str = "Score Contributions"
) -> plt.Figure:
    """
    Plot score contributions from different components.
    
    Args:
        penalties: Dictionary of penalty term values
        weights: Dictionary of weights for each term
        X_star: Effective roundtrips
        title: Plot title
        
    Returns:
        Matplotlib figure
    """
    # Calculate contributions
    contributions = {}
    contributions['αX*'] = weights['alpha'] * X_star
    
    for key in ['p', 'b', 's', 'r']:
        penalty_key = {'p': 'beta', 'b': 'gamma', 's': 'delta', 'r': 'eta'}[key]
        contributions[f'-{penalty_key[0]}{key}'] = -weights[penalty_key] * penalties.get(key, 0)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Plot bar chart
    labels = list(contributions.keys())
    values = list(contributions.values())
    
    bars = ax.bar(labels, values, color=['green' if v >= 0 else 'red' for v in values])
    
    # Add value labels on bars
    for bar, value in zip(bars, values):
        ax.text(
            bar.get_x() + bar.get_width()/2,
            bar.get_height() + (0.5 if value >= 0 else -1),
            f'{value:.2f}',
            ha='center',
            va='bottom' if value >= 0 else 'top'
        )
    
    # Formatting
    ax.set_title(title)
    ax.set_ylabel('Contribution to Score')
    ax.grid(True, alpha=0.3, axis='y')
    
    return fig


# Example usage (for testing)
if __name__ == "__main__":
    print("Plotting module loaded successfully.")
    print("Functions available:")
    print("- plot_box_bounds(df, qlo, qhi, mid, title)")
    print("- plot_discretized_series(df, states, title)")
    print("- plot_roundtrips(df, states, trip_stats, title)")
    print("- plot_score_contributions(penalties, weights, X_star, title)")