"""
Report generation module for box pattern analysis.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List
import warnings
import os
from datetime import datetime


def generate_summary_report(
    results: pd.DataFrame,
    config: Dict[str, Any],
    output_dir: str = "artifacts/reports"
) -> str:
    """
    Generate a summary HTML report.
    
    Args:
        results: DataFrame with scan results
        config: Configuration dictionary
        output_dir: Directory to save the report
        
    Returns:
        Path to the generated report
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate timestamp for the report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create report filename
    report_filename = f"box_scan_report_{timestamp}.html"
    report_path = os.path.join(output_dir, report_filename)
    
    # Start building the HTML report
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Box Pattern Scan Report</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            .summary {{
                background-color: #f9f9f9;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }}
        </style>
    </head>
    <body>
        <h1>Box Pattern Scan Report</h1>
        <p>Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        
        <div class="summary">
            <h2>Summary</h2>
            <p>Total symbols scanned: {len(results['symbol'].unique()) if not results.empty else 0}</p>
            <p>Total box patterns identified: {len(results)}</p>
            <p>Average score: {results['score'].mean():.2f} (std: {results['score'].std():.2f})</p>
        </div>
        
        <h2>Configuration</h2>
        <pre>{format_config(config)}</pre>
        
        <h2>Top Box Patterns</h2>
        {generate_top_patterns_table(results)}
        
        <h2>Score Distribution</h2>
        <p>Histogram of scores would be here in a full implementation.</p>
        
        <h2>Performance Metrics</h2>
        <p>Detailed performance metrics would be here in a full implementation.</p>
    </body>
    </html>
    """
    
    # Write the report to file
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return report_path


def format_config(config: Dict[str, Any]) -> str:
    """
    Format configuration dictionary as a string for display.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Formatted string
    """
    lines = []
    for key, value in config.items():
        if isinstance(value, dict):
            lines.append(f"{key}:")
            for subkey, subvalue in value.items():
                lines.append(f"  {subkey}: {subvalue}")
        else:
            lines.append(f"{key}: {value}")
    return "\n".join(lines)


def generate_top_patterns_table(results: pd.DataFrame, top_k: int = 20) -> str:
    """
    Generate HTML table for top box patterns.
    
    Args:
        results: DataFrame with scan results
        top_k: Number of top patterns to include
        
    Returns:
        HTML table as string
    """
    if results.empty:
        return "<p>No box patterns identified.</p>"
    
    # Select top patterns by score
    top_results = results.nlargest(top_k, 'score')
    
    # Select columns to display
    columns_to_show = [
        'symbol', 'lookback', 'score', 'X', 'X_star', 
        'breakout_proportion', 'max_breakout', 'start_time', 'end_time'
    ]
    
    # Filter to only include columns that exist in the DataFrame
    existing_columns = [col for col in columns_to_show if col in top_results.columns]
    top_results = top_results[existing_columns]
    
    # Convert to HTML table
    html_table = top_results.to_html(
        index=False, 
        table_id="top_patterns",
        escape=False,
        float_format="{:.4f}".format
    )
    
    return html_table


def generate_detailed_pattern_report(
    pattern_data: Dict[str, Any],
    output_dir: str = "artifacts/reports"
) -> str:
    """
    Generate a detailed report for a specific box pattern.
    
    Args:
        pattern_data: Dictionary with pattern data
        output_dir: Directory to save the report
        
    Returns:
        Path to the generated report
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate timestamp for the report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create report filename
    symbol = pattern_data.get('symbol', 'unknown')
    lookback = pattern_data.get('lookback', 'unknown')
    report_filename = f"pattern_detail_{symbol}_{lookback}_{timestamp}.html"
    report_path = os.path.join(output_dir, report_filename)
    
    # Start building the HTML report
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Box Pattern Detail Report - {symbol}</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            .pattern-info {{
                background-color: #f9f9f9;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }}
        </style>
    </head>
    <body>
        <h1>Box Pattern Detail Report</h1>
        <p>Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        
        <div class="pattern-info">
            <h2>Pattern Information</h2>
            <table>
                <tr><th>Symbol</th><td>{symbol}</td></tr>
                <tr><th>Lookback Period</th><td>{lookback}</td></tr>
                <tr><th>Score</th><td>{pattern_data.get('score', 'N/A'):.4f}</td></tr>
                <tr><th>Effective Roundtrips (X*)</th><td>{pattern_data.get('X_star', 'N/A')}</td></tr>
                <tr><th>Total Roundtrips (X)</th><td>{pattern_data.get('X', 'N/A')}</td></tr>
                <tr><th>Start Time</th><td>{pattern_data.get('start_time', 'N/A')}</td></tr>
                <tr><th>End Time</th><td>{pattern_data.get('end_time', 'N/A')}</td></tr>
            </table>
        </div>
        
        <h2>Penalty Terms</h2>
        <table>
            <tr><th>Term</th><th>Value</th></tr>
            <tr><td>Breakout Proportion (p)</td><td>{pattern_data.get('breakout_proportion', 'N/A'):.4f}</td></tr>
            <tr><td>Max Breakout (b)</td><td>{pattern_data.get('max_breakout', 'N/A'):.4f}</td></tr>
            <tr><td>Sticky Edge (s)</td><td>{pattern_data.get('sticky_edge', 'N/A'):.4f}</td></tr>
            <tr><td>Mean Reversion (r)</td><td>{pattern_data.get('mean_reversion', 'N/A'):.4f}</td></tr>
        </table>
        
        <h2>Visualizations</h2>
        <p>Charts and visualizations would be here in a full implementation.</p>
        
        <h2>Trading Recommendations</h2>
        <p>Trading recommendations would be here in a full implementation.</p>
    </body>
    </html>
    """
    
    # Write the report to file
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return report_path


# Example usage (for testing)
if __name__ == "__main__":
    print("Report module loaded successfully.")
    print("Functions available:")
    print("- generate_summary_report(results, config, output_dir)")
    print("- generate_detailed_pattern_report(pattern_data, output_dir)")