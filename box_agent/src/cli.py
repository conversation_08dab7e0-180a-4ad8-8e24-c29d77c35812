"""
Command-line interface for the box scanner.
"""

import argparse
import pandas as pd
import yaml
import os
import sys
from typing import Dict, Any
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_loader import load_kline_data, resample_kline_data
from src.box_scanner import scan_symbol
from src.backtest import walk_forward_backtest
from src.report import generate_summary_report


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def prepare_data(symbols: list, timeframe: str, start_date: str, end_date: str = None):
    """
    Prepare data for scanning.
    In a full implementation, this would load raw data and process it.
    For now, we'll just print a message.
    """
    print(f"Preparing data for symbols: {symbols}")
    print(f"Timeframe: {timeframe}")
    print(f"Start date: {start_date}")
    if end_date:
        print(f"End date: {end_date}")
    
    # In a real implementation, this would:
    # 1. Load raw data from data/raw/
    # 2. Validate and clean the data
    # 3. Resample to the desired timeframe if needed
    # 4. Save processed data to data/proc/
    
    print("Data preparation completed.")


def scan(config_path: str):
    """
    Scan for box patterns.
    """
    # Load configuration
    config = load_config(config_path)
    
    # Get symbols to scan
    symbols = config['universe']['symbols']
    
    # Initialize results list
    all_results = []
    
    # Scan each symbol
    for symbol in symbols:
        print(f"Scanning symbol: {symbol}")
        
        # Load data
        # In a real implementation, we would load from data/proc/
        # For now, we'll check if the data file exists in the data/raw/ directory
        # Use a path relative to the script file, not the current working directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_dir = os.path.dirname(script_dir)  # This is the correct project directory
        data_file = os.path.join(project_dir, "data", "raw", f"{symbol}_4h.json")
        if os.path.exists(data_file):
            try:
                df = load_kline_data(data_file, config['bar']['timeframe'])
                print(f"Loaded {len(df)} bars for {symbol}")
            except Exception as e:
                print(f"Error loading data for {symbol}: {e}")
                continue
        else:
            print(f"Data file {data_file} not found for {symbol}")
            continue
        
        # Scan for box patterns
        try:
            results = scan_symbol(df, config['bar']['lookbacks'], config)
            if not results.empty:
                # Add symbol column if not present
                if 'symbol' not in results.columns:
                    results['symbol'] = symbol
                all_results.append(results)
                print(f"Found {len(results)} box patterns for {symbol}")
            else:
                print(f"No box patterns found for {symbol}")
        except Exception as e:
            print(f"Error scanning {symbol}: {e}")
    
    # Combine all results
    if all_results:
        combined_results = pd.concat(all_results, ignore_index=True)
        
        # Save results
        output_dir = "artifacts/signals"
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = os.path.join(output_dir, f"box_scan_results_{timestamp}.csv")
        combined_results.to_csv(results_file, index=False)
        print(f"Results saved to {results_file}")
        
        # Generate summary report
        if config['output']['write_html']:
            report_path = generate_summary_report(combined_results, config)
            print(f"Summary report saved to {report_path}")
        
        return combined_results
    else:
        print("No box patterns found for any symbol.")
        return pd.DataFrame()


def report(last_run: bool = False, run_id: str = None):
    """
    Generate a detailed report.
    """
    # In a full implementation, this would:
    # 1. Load the results from the last run or specified run
    # 2. Generate detailed reports for each pattern
    # 3. Create visualizations
    
    print("Generating detailed report...")
    
    if last_run:
        # Find the most recent results file
        output_dir = "artifacts/signals"
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.startswith("box_scan_results_") and f.endswith(".csv")]
            if files:
                # Sort by timestamp to get the most recent
                files.sort(reverse=True)
                latest_file = files[0]
                results_path = os.path.join(output_dir, latest_file)
                
                print(f"Loading results from {results_path}")
                try:
                    results = pd.read_csv(results_path)
                    print(f"Loaded {len(results)} results")
                    
                    # In a full implementation, we would generate detailed reports
                    # for each pattern and create visualizations
                    print("Detailed report generation completed.")
                    return results
                except Exception as e:
                    print(f"Error loading results: {e}")
            else:
                print("No results files found.")
        else:
            print("Results directory does not exist.")
    else:
        print("Report generation requires either --last-run or --run-id parameter.")
    
    return None


def main():
    """Main entry point for the CLI."""
    parser = argparse.ArgumentParser(description="Box Pattern Scanner")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Prepare data command
    prepare_parser = subparsers.add_parser('prepare-data', help='Prepare data for scanning')
    prepare_parser.add_argument('--symbols', nargs='+', required=True, help='Symbols to prepare')
    prepare_parser.add_argument('--timeframe', required=True, help='Timeframe (e.g., 1h, 4h, 12h)')
    prepare_parser.add_argument('--start', required=True, help='Start date (YYYY-MM-DD)')
    prepare_parser.add_argument('--end', help='End date (YYYY-MM-DD)')
    
    # Scan command
    scan_parser = subparsers.add_parser('scan', help='Scan for box patterns')
    scan_parser.add_argument('--config', default='config/default.yaml', help='Path to config file')
    
    # Report command
    report_parser = subparsers.add_parser('report', help='Generate detailed report')
    report_parser.add_argument('--last-run', action='store_true', help='Generate report for last run')
    report_parser.add_argument('--run-id', help='Generate report for specific run ID')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Execute command
    if args.command == 'prepare-data':
        prepare_data(args.symbols, args.timeframe, args.start, args.end)
    elif args.command == 'scan':
        scan(args.config)
    elif args.command == 'report':
        report(args.last_run, args.run_id)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()