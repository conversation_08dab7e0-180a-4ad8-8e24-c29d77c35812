"""
Box scanner module for discretization, roundtrip counting, penalties, and scoring.
"""

import pandas as pd
import numpy as np
from typing import Tuple, List, Dict, Any
from dataclasses import dataclass


@dataclass
class TripStats:
    """Statistics for a single roundtrip."""
    start_time: pd.Timestamp
    end_time: pd.Timestamp
    start_price: float
    end_price: float
    holding_bars: int
    theoretical_return: float  # In basis points


def discretize_series(
    close: pd.Series, 
    qlo: pd.Series, 
    qhi: pd.Series
) -> pd.Series:
    """
    Discretize close price series into Low, Mid, High states.
    
    Args:
        close: Close price series
        qlo: Lower quantile series
        qhi: Upper quantile series
        
    Returns:
        Series with values 'Low', 'Mid', 'High'
    """
    states = pd.Series('Mid', index=close.index, dtype=object)
    states[close < qlo] = 'Low'
    states[close > qhi] = 'High'
    return states


def count_effective_roundtrips(
    close: pd.Series,
    states: pd.Series,
    qlo: pd.Series,
    qhi: pd.Series,
    fees_bps: float,
    slip_bps: float,
    min_hold: int
) -> Tuple[int, int, List[TripStats]]:
    """
    Count effective roundtrips (Low↔High transitions) that are profitable after costs.
    
    Args:
        close: Close price series
        states: Discretized states series (Low, Mid, High)
        qlo: Lower quantile series
        qhi: Upper quantile series
        fees_bps: Transaction fees in basis points
        slip_bps: Slippage in basis points
        min_hold: Minimum holding bars
        
    Returns:
        Tuple of (X, X_star, trip_stats)
        X: Total roundtrips
        X_star: Effective roundtrips (profitable after costs)
        trip_stats: List of statistics for each effective roundtrip
    """
    # Remove 'Mid' states to get only 'Low' and 'High'
    lh_states = states[states != 'Mid']
    
    # If we don't have at least 2 states, no roundtrips possible
    if len(lh_states) < 2:
        return 0, 0, []
    
    # Count total roundtrips
    changes = lh_states != lh_states.shift(1)
    X = changes.sum() // 2  # Each roundtrip is two changes (Low->High->Low or High->Low->High)
    
    # Count effective roundtrips
    X_star = 0
    trip_stats = []
    
    # Track current position
    current_state = lh_states.iloc[0]
    entry_time = lh_states.index[0]
    entry_price = close[entry_time]
    holding_bars = 0
    
    # Iterate through state changes
    for i in range(1, len(lh_states)):
        new_state = lh_states.iloc[i]
        holding_bars += 1
        
        # If state changed
        if new_state != current_state:
            exit_time = lh_states.index[i]
            exit_price = close[exit_time]
            
            # Calculate theoretical return (from entry to exit)
            if current_state == 'Low' and new_state == 'High':
                # Long position from Low to High
                theoretical_return = (exit_price / entry_price - 1) * 10000  # In bps
            elif current_state == 'High' and new_state == 'Low':
                # Short position from High to Low
                theoretical_return = (entry_price / exit_price - 1) * 10000  # In bps
            else:
                # This shouldn't happen with our discretization
                theoretical_return = 0
            
            # Calculate minimum required return to cover costs
            min_return = fees_bps * 2 + slip_bps * 2  # Entry + exit for both fees and slippage
            
            # Check if roundtrip is effective
            if (theoretical_return >= min_return and 
                holding_bars >= min_hold):
                X_star += 1
                trip_stats.append(TripStats(
                    start_time=entry_time,
                    end_time=exit_time,
                    start_price=entry_price,
                    end_price=exit_price,
                    holding_bars=holding_bars,
                    theoretical_return=theoretical_return
                ))
            
            # Reset for next potential roundtrip
            current_state = new_state
            entry_time = exit_time
            entry_price = exit_price
            holding_bars = 0
        else:
            # State didn't change, continue holding
            pass
    
    return X, X_star, trip_stats


def penalty_terms(
    close: pd.Series,
    qlo: pd.Series,
    qhi: pd.Series
) -> Dict[str, pd.Series]:
    """
    Calculate penalty terms for box scoring.
    
    Args:
        close: Close price series
        qlo: Lower quantile series
        qhi: Upper quantile series
        
    Returns:
        Dictionary of penalty terms as pandas Series
    """
    # Breakout proportion: proportion of close prices outside bounds
    outside = (close < qlo) | (close > qhi)
    p = outside.rolling(window=len(close)).mean()  # This should be calculated per window
    
    # For simplicity, we'll calculate it for the entire series
    # In a full implementation, this would be calculated per rolling window
    p = pd.Series([outside.mean()] * len(close), index=close.index)
    
    # Maximum breakout magnitude (normalized by bandwidth)
    bw = qhi - qlo
    upper_breakout = np.maximum(close - qhi, 0)
    lower_breakout = np.maximum(qlo - close, 0)
    max_breakout = np.maximum(upper_breakout, lower_breakout)
    b = max_breakout / bw
    
    # Sticky edge time: time spent outside bounds without returning
    # This is a simplified calculation
    sticky = pd.Series(0.0, index=close.index)
    # In a full implementation, this would require tracking consecutive periods outside bounds
    
    # For now, we'll just use a simple measure
    sticky = outside.astype(float)
    
    # Mean reversion strength: negative correlation between normalized price and next return
    # This is also simplified
    m = (qlo + qhi) / 2
    z_t = (close - m) / bw
    next_return = close.pct_change().shift(-1)
    # In a full implementation, this would be calculated per window
    r = pd.Series([-0.1] * len(close), index=close.index)  # Placeholder
    
    return {
        'p': p,  # Breakout proportion
        'b': b,  # Max breakout magnitude
        's': sticky,  # Sticky edge time
        'r': r   # Mean reversion strength
    }


def score_window(
    X_star: int,
    penalties: Dict[str, float],  # Single values, not series
    weights: Dict[str, float],
    liquidity_ok: bool,
    cost_risk: float
) -> float:
    """
    Calculate score for a window.
    
    Args:
        X_star: Effective roundtrips
        penalties: Dictionary of penalty term values
        weights: Dictionary of weights for each term
        liquidity_ok: Whether liquidity requirements are met
        cost_risk: Cost risk measure
        
    Returns:
        Score for the window
    """
    # Unpack penalties
    p = penalties.get('p', 0)
    b = penalties.get('b', 0)
    s = penalties.get('s', 0)
    r = penalties.get('r', 0)
    
    # Calculate score
    score = (
        weights['alpha'] * X_star -
        weights['beta'] * p -
        weights['gamma'] * b -
        weights['delta'] * s +
        weights['eta'] * r +
        weights['lambda'] * liquidity_ok -
        weights['zeta'] * cost_risk
    )
    
    return score


# Example usage (for testing)
if __name__ == "__main__":
    print("Box scanner module loaded successfully.")
    print("Functions available:")
    print("- discretize_series(close, qlo, qhi)")
    print("- count_effective_roundtrips(close, states, qlo, qhi, fees_bps, slip_bps, min_hold)")
    print("- penalty_terms(close, qlo, qhi)")
    print("- score_window(X_star, penalties, weights, liquidity_ok, cost_risk)")


def scan_symbol(
    df: pd.DataFrame,
    lookbacks: List[int],
    config: Dict[str, Any]
) -> pd.DataFrame:
    """
    Scan a symbol for box patterns across multiple lookback windows.
    
    Args:
        df: DataFrame with K-line data
        lookbacks: List of lookback window sizes
        config: Configuration dictionary
        
    Returns:
        DataFrame with results for each window
    """
    from .quantile_online import rolling_box_bounds
    
    results = []
    
    for W in lookbacks:
        # Calculate box bounds
        qlo, qhi, m, bw = rolling_box_bounds(
            df['close'],
            W,
            config['quantiles']['q_lo'],
            config['quantiles']['q_hi'],
            method="sliding_window"  # Using sliding window for simplicity
        )
        
        # Discretize series
        states = discretize_series(df['close'], qlo, qhi)
        
        # Count effective roundtrips
        X, X_star, trip_stats = count_effective_roundtrips(
            df['close'], states, qlo, qhi,
            config['costs']['taker_fee_bps'],
            config['costs']['slippage_bps'],
            config['scoring']['min_holding_bars']
        )
        
        # Calculate penalty terms (simplified)
        pen = penalty_terms(df['close'], qlo, qhi)
        
        # Calculate cost risk (simplified)
        cost_risk = 0.0  # Placeholder
        
        # Check liquidity (simplified)
        liquidity_ok = True  # Placeholder
        
        # Calculate score for this window
        score = score_window(
            X_star,
            {k: v.mean() for k, v in pen.items()},  # Average penalty terms
            config['scoring']['weights'],
            liquidity_ok,
            cost_risk
        )
        
        # Pack results
        results.append({
            'symbol': config.get('symbol', 'unknown'),
            'lookback': W,
            'score': score,
            'X': X,
            'X_star': X_star,
            'breakout_proportion': pen['p'].mean(),
            'max_breakout': pen['b'].mean(),
            'sticky_edge': pen['s'].mean(),
            'mean_reversion': pen['r'].mean(),
            'start_time': df.index[W-1],  # First valid timestamp
            'end_time': df.index[-1],     # Last timestamp
        })
    
    # Convert to DataFrame and rank
    results_df = pd.DataFrame(results)
    if not results_df.empty:
        results_df['rank'] = results_df['score'].rank(ascending=False)
        results_df = results_df.sort_values('rank')
    
    return results_df