## 0. 背景与目标

**目标**：自动扫描加密资产的K线数据，在不同时间尺度上发现“箱体震荡”区间，对其“可交易性”打分与排序，并可选输出对应的入场/出场建议供后续策略使用。

**核心思想**：

* 用滚动分位数定义箱体上/下边界（避免前视）。
* 将价格离散为 {Low, Mid, High}，删除 Mid，统计 High↔Low 的往返次数（奖励“可交易来回”）。
* 对突破频次/幅度、贴边磨损、带宽过窄/过宽、流动性不足、交易成本覆盖度等施加惩罚。
* 采用 walk-forward（滚动）与 purged/embargo 时间序列交叉验证评估稳健性。

**成功标准（硬性）**：

* 算法在近 12–24 个月、主流交易对上，**净 alpha 在费后为正**，回测夏普 > 1、最大回撤可控（< 20–30%，按策略风险设定）。
* 箱体检测在不同周期（1h/4h/12h）上**可重复发现**高分区间，且 live-run 与回测分布一致性良好（KS检验不过度偏离）。
* 代码具备**可复现性**（固定随机种子、环境锁定、数据切分一致）、**可扩展性**（新交易对/新周期在 \<N 小时内全量扫描）。

---

## 1. 数据与接口

### 1.1 K线数据

* **字段**：timestamp, open, high, low, close, volume（必要）；可选：成交额、买卖深度快照。
* **粒度**：1h、4h、12h（先做 4h 作为默认）。
* **时区**：统一 UTC，内部转换时保持 tz-aware。

**数据格式**:

```json
[
  [
    1499040000000,      // 开盘时间
    "0.01634790",       // 开盘价
    "0.80000000",       // 最高价
    "0.01575800",       // 最低价
    "0.01577100",       // 收盘价(当前K线未结束的即为最新价)
    "148976.11427815",  // 成交量
    1499644799999,      // 收盘时间
    "2434.19055334",    // 成交额
    308,                // 成交笔数
    "1756.87402397",    // 主动买入成交量
    "28.46694368",      // 主动买入成交额
    "17928899.62484339" // 请忽略该参数
  ]
]
```

### 1.2 交易成本/约束

* **手续费**：taker/maker（默认使用 taker）。
* **滑点模型**：按价差占比或按成交量深度线性估算；最小以 N tick 起算。
* **资金费（永续）**：可选输入；若缺省，默认 0 或用近似区间均值。

### 1.3 资产过滤

* **黑名单**：新币、停牌、异常波动（涨跌停样式）剔除。
* **流动性阈值**：近 W 窗口平均成交量/金额下限。

---

## 2. 算法规格（可编码）

### 2.1 箱体定义（滚动、无前视）

对时间序列价格 $P_t$：

* 选择窗口长度 $W$（以 K 根计）。
* 在每个时间点 $t$ 上，以历史窗 $[t-W+1, t]$ 的价格集合计算分位数：

  * $Q_{lo} = Q_{p_{lo}}(P_{t-W+1:t})$（如 0.10）
  * $Q_{hi} = Q_{p_{hi}}(P_{t-W+1:t})$（如 0.90）
  * 中线 $m = (Q_{lo} + Q_{hi})/2$
  * 带宽 $bw = Q_{hi} - Q_{lo}$
* **在线/近似分位实现**：优先 t-digest 或 GK 算法；初版可用滑动窗口+分桶近似。

### 2.2 离散序列与往返计数

* 对每根K线收盘价 $C_t$ 进行离散化：

  * $C_t < Q_{lo} \Rightarrow \text{Low}$
  * $C_t > Q_{hi} \Rightarrow \text{High}$
  * 其它为 Mid
* **去 Mid**：得到仅含 {Low, High} 的序列 $S$（保留原时间戳）。
* **往返次数 $X$**：遍历 $S$，每当相邻项不同（Low↔High）计数 +1。
* **有效往返 $X^\*$**（成本约束）：仅统计满足

  * **振幅**：相邻两次状态间对应的极值差或从边到边理论可得利润 $\ge c \cdot \text{(费率+滑点)}$
  * **最小持时**：跨越的K线数量 $\ge H_{min}$
    的往返。

### 2.3 惩罚项与附加指标

* **突破占比 $p$**：过去 W 内收盘价在边界外（>Qhi 或 \<Qlo）的比例。
* **最大突破幅度 $b$**：边界外最大偏离（按 bw 归一化）。
* **贴边磨损 $s$**：在边界外但未对侧返场的“贴边停留”时间占比。
* **带宽惩罚 $f$**：过窄（不足以覆盖最小可行价差）或过宽（更像趋势）给予负分。
* **均值回归强度 $r$**：定义为 $\text{corr}(z_t, \Delta P_{t+1})$ 的负相关（其中 $z_t = (C_t-m)/bw$）；更负更佳。
* **流动性过滤 $l$**：成交量/金额低于阈值时整体降权或归零。

### 2.4 评分函数（可配置权重）

$$
\text{Score} = \alpha X^\* - \beta p - \gamma b - \delta s + \eta r + \lambda l - \zeta \text{CostRisk}
$$

* **CostRisk**：对（最小可行价差/成本）比值过低的窗口施以强惩罚。
* 超参 $\alpha,\beta,\gamma,\delta,\eta,\lambda,\zeta$ 通过网格或贝叶斯优化在**训练集**上调，**验证集**选择。

### 2.5 周期与多窗口扫描

* 扫描多个 $W$：如 30、60、120、240 根（依周期而变）。
* 多周期（1h/4h/12h）并行；对同一资产输出 Pareto-前沿候选（在\[高分、低突破、高回转效率]上不可同时被支配）。

### 2.6 策略接口（可选）

* **入场**：价到 Qhi 做空 / 到 Qlo 做多（或反向突破过滤）。
* **出场**：回到中线/对侧边界/固定 R 倍；**止损**：边外连续 k 根或最大不利波动 MAE。
* **仓位**：Kelly 降杠或波动率目标（如 10% 年化波动）。

---

## 3. 回测与验证

### 3.1 数据切分（严格防泄漏）

* **起始冷启动**：每个 W 需先积累 W 根。
* **walk-forward**：以月/季为步长滚动：用过去 $T_{train}$ 调参，下一段 $T_{test}$ 评估，随后滑窗前移。
* **Purged/Embargo K-Fold**：K=5；fold 间留出 embargo（如 1–2W）避免信息泄露与重叠。

### 3.2 评估指标

* 扫描器层面：每资产每周期的**Score 分布**、候选覆盖率、稳定性（窗口间 Rank IC）。
* 策略层面：年化收益/波动/夏普、卡玛比、最大回撤、Calmar、MDD 恢复期、盈亏单分布、命中率、期望收益、换手、费用占比、滑点灵敏度。
* **稳健性试验**：成本上浮、滑点翻倍、参数 ±10–20% 扰动、剔除高分前 10% 的依赖性（防过拟合）。

### 3.3 可视化与可解释

* 箱体边界随时间曲线（Qlo/Qhi/m）。
* 离散序列与有效往返标记。
* 得分分解（贡献条形图）：$\alpha X^\*, \beta p, \dots$ 逐项贡献。

---

## 4. 落地与产出

### 4.1 产出制品

* **候选箱体表**：资产、周期、时间窗起止、Score、各子指标、建议操作（可选）。
* **信号流**：Kafka/文件队列/数据库，用于后续执行系统。
* **报告**：HTML/Notebook（概览 + drilldown）。

### 4.2 配置与参数（示例默认）

```yaml
universe:
  symbols: ["BTCUSDT","ETHUSDT","SOLUSDT"]
  min_liquidity_usd: 1000000
bar:
  timeframe: "4h"
  lookbacks: [60, 120, 240]
quantiles:
  q_lo: 0.10
  q_hi: 0.90
scoring:
  min_holding_bars: 3
  min_edge_return_bps: 8   # 边到边至少覆盖(费+滑点)的系数
  weights:
    alpha: 1.0   # X*
    beta:  2.0   # p
    gamma: 1.0   # b
    delta: 0.5   # s
    eta:   0.5   # r
    lambda: 0.2  # l
    zeta:  1.0   # CostRisk
costs:
  taker_fee_bps: 5
  slippage_bps: 3
  funding_bps_per_day: 0
backtest:
  walk_forward:
    train_days: 180
    test_days: 30
    embargo_days: 3
output:
  top_k: 20
  write_html: true
```

---

## 5. 代码结构（文件树）

```
project/
  README.md
  pyproject.toml / requirements.txt
  config/
    default.yaml
  data/
    raw/    # 原始K线
    proc/   # 对齐/去重/校验后的K线
  src/
    data_loader.py          # 读取/校验/对齐/重采样
    quantile_online.py      # t-digest/GK 或滑窗分位
    box_scanner.py          # 离散化、往返计数、惩罚项、评分
    cost_models.py          # 手续费、滑点、资金费估算
    liquidity.py            # 流动性过滤
    backtest.py             # walk-forward + purged K-fold
    metrics.py              # 绩效与稳健性指标
    plotting.py             # 可视化（单图单轴，不强设色）
    report.py               # 汇总报告（HTML/nb）
    cli.py                  # 命令行入口
  notebooks/
    exploration.ipynb
  tests/
    test_quantile_online.py
    test_box_scanner.py
    test_backtest_split.py
  artifacts/
    reports/
    signals/
```

---

## 6. 关键函数与伪代码

### 6.1 分位与箱体边界

```python
def rolling_box_bounds(prices, window, q_lo=0.1, q_hi=0.9, method="tdigest"):
    # 返回同长度的 Qlo, Qhi, m, bw 数组（前 window-1 为空）
```

### 6.2 离散与往返

```python
def discretize_series(close, qlo, qhi):
    # 返回 ['Low'|'Mid'|'High']，长度与close一致

def count_effective_roundtrips(close, states, qlo, qhi, fees_bps, slip_bps, min_hold):
    # 删除 Mid，遍历 Low/High 序列，计算 X 与 X*
    # 同时记录每次来回的价差、持时，过滤掉不足以覆盖成本的
    return X, X_star, trip_stats
```

### 6.3 惩罚项与评分

```python
def penalty_terms(close, qlo, qhi):
    p = proportion_outside_bounds(close, qlo, qhi)
    b = max_breakout_magnitude(close, qlo, qhi) / (qhi - qlo)
    s = sticky_edge_time(close, qlo, qhi)
    r = -corr(z_t, next_return)  # 均值回归强度
    return dict(p=p, b=b, s=s, r=r)

def score_window(X_star, penalties, weights, liquidity_ok, cost_risk):
    # Score = αX* - βp - γb - δs + ηr + λl - ζCostRisk
```

### 6.4 扫描器主流程

```python
def scan_symbol(df, lookbacks, config):
    results = []
    for W in lookbacks:
        qlo, qhi, m, bw = rolling_box_bounds(df.close, W, ...)
        states = discretize_series(df.close, qlo, qhi)
        X, X_star, _ = count_effective_roundtrips(...)
        pen = penalty_terms(df.close, qlo, qhi)
        cost_risk = compute_cost_risk(...)
        score = score_window(X_star, pen, weights, liquidity_ok(...), cost_risk)
        results.append(pack(W, score, pen, X_star, meta...))
    return rank_and_filter(results)
```

### 6.5 回测拆分

```python
def walk_forward_backtest(data, config):
    # 滚动训练/验证；训练期仅用于超参选择（如分位、权重），
    # 测试期严格只用过去信息计算边界并生成信号，评估绩效。
```

---

## 7. 测试与验收

### 7.1 单元测试

* **分位正确性**：固定序列的 Qlo/Qhi 与期望一致；异常值不致崩溃。
* **往返计数**：构造 toy 序列（High↔Low 交替），X 与 X\* 可预测。
* **防前视**：在测试里确保任何时刻计算仅使用 $\le t$ 的数据。

### 7.2 集成测试

* 读入一段真实K线（小样本），运行全流程，产出候选箱体表与图表文件，校验字段完整性与非空。

### 7.3 性能与稳健

* 在 2k 根K线、3 个 lookback 上，单币扫描 < 1s（基线）；多核并行下 100 币 < 10min（示例目标）。
* 成本上浮 50% 时策略仍为正或下降可解释。

---

## 8. 运行方式（CLI 示例）

```bash
# 1) 预处理
python -m src.cli prepare-data --symbols BTCUSDT ETHUSDT --timeframe 4h --start 2023-01-01

# 2) 扫描 + 回测
python -m src.cli scan --config config/default.yaml

# 3) 生成报告
python -m src.cli report --last-run
```

---

## 9. Agent 执行任务卡（可直接投喂）

**任务 1：环境与骨架**

* 读取本方案，创建文件树与基础依赖（pandas, numpy, numba/tqdm，可选 tdigest）。
* 实现 `data_loader.py`（CSV/Parquet 输入，时区、缺失/重复处理，OHLC 校验）。

**任务 2：分位数与箱体边界模块**

* 实现 `rolling_box_bounds`，含滑窗与 t-digest 两种实现；对比性能并写基准。

**任务 3：离散化与往返计数**

* 实现 `discretize_series`、`count_effective_roundtrips`，并产出 trip 明细。

**任务 4：惩罚项与评分**

* 实现 `penalty_terms`、`score_window`，将超参接入 YAML。

**任务 5：扫描与排名**

* `box_scanner.py`：对单币多窗口计算，输出 DataFrame（含 Score、子项、时间窗）。

**任务 6：成本/流动性与过滤**

* `cost_models.py`、`liquidity.py`：实现最小可行价差检查与流动性阈值。

**任务 7：回测与验证**

* `backtest.py`：walk-forward + purged K-Fold；产出绩效字典与曲线。

**任务 8：可视化与报告**

* `plotting.py`：边界/离散/往返标注图；`report.py` 汇总 HTML。

**任务 9：测试与CI**

* 在 `tests/` 下补齐单元测试；GitHub Actions（或本地CI）跑 `pytest -q`。

**任务 10：实验记录与复现**

* 固定随机种子，保存 `config` 与 `artifacts`；记录每次 run 的哈希与指标。

---

## 10. 风险与对策清单

* **前视偏差**：只用 $[t-W+1, t]$ 数据计算；回测时严格时序推进。
* **多重检验**：在报告中提供 FWER 控制（如 Bonferroni 上限提醒）与 out-of-sample 一致性图。
* **滑点低估**：提供“滑点 × {1,2,3}”灵敏度面板。
* **异常蜡烛**：对极端长影线设置 winsorize 或中位数平滑开关。
* **低流动性**：量/额阈值过滤；深夜时段可降权。
* **过拟合权重**：权重选择只基于训练集；验证/测试分离，并做参数扰动稳定性检验。

---

## 11. 里程碑与时间表（建议）

* **D1–D2**：数据模块 + 分位数模块（含基准测试）。
* **D3**：离散/往返/惩罚/评分，完成单币单周期跑通。
* **D4**：多窗口/多周期扫描，导出候选表与可视化。
* **D5–D6**：walk-forward & purged K-Fold 回测与报告；成本/滑点灵敏度。
* **D7**：整理 README、配置模板、自动化脚本与测试用例。
