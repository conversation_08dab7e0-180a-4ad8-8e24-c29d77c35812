"""
Unit tests for the quantile_online module.
"""

import unittest
import numpy as np
import pandas as pd
from src.quantile_online import (
    rolling_box_bounds,
    _rolling_box_bounds_sliding_window,
    _rolling_box_bounds_tdigest
)


class TestQuantileOnline(unittest.TestCase):
    
    def setUp(self):
        """Set up test data."""
        # Generate sample data
        np.random.seed(42)
        n = 1000
        self.prices = pd.Series(
            100 + np.cumsum(np.random.randn(n) * 0.1), 
            name="close"
        )
        self.window = 100
        self.q_lo = 0.1
        self.q_hi = 0.9
    
    def test_rolling_box_bounds_sliding_window(self):
        """Test sliding window method for rolling box bounds."""
        qlo, qhi, mid, bw = _rolling_box_bounds_sliding_window(
            self.prices, self.window, self.q_lo, self.q_hi
        )
        
        # Check that the first window-1 values are NaN
        self.assertTrue(qlo.iloc[:self.window-1].isna().all())
        self.assertTrue(qhi.iloc[:self.window-1].isna().all())
        self.assertTrue(mid.iloc[:self.window-1].isna().all())
        self.assertTrue(bw.iloc[:self.window-1].isna().all())
        
        # Check that subsequent values are not NaN
        self.assertFalse(qlo.iloc[self.window-1:].isna().any())
        self.assertFalse(qhi.iloc[self.window-1:].isna().any())
        self.assertFalse(mid.iloc[self.window-1:].isna().any())
        self.assertFalse(bw.iloc[self.window-1:].isna().any())
        
        # Check that Qlo < Mid < Qhi
        self.assertTrue((qlo.iloc[self.window-1:] < mid.iloc[self.window-1:]).all())
        self.assertTrue((mid.iloc[self.window-1:] < qhi.iloc[self.window-1:]).all())
        
        # Check that bandwidth is positive
        self.assertTrue((bw.iloc[self.window-1:] > 0).all())
    
    def test_rolling_box_bounds_with_sliding_window_method(self):
        """Test rolling_box_bounds function with sliding window method."""
        qlo, qhi, mid, bw = rolling_box_bounds(
            self.prices, self.window, self.q_lo, self.q_hi, method="sliding_window"
        )
        
        # Check that the first window-1 values are NaN
        self.assertTrue(qlo.iloc[:self.window-1].isna().all())
        self.assertTrue(qhi.iloc[:self.window-1].isna().all())
        self.assertTrue(mid.iloc[:self.window-1].isna().all())
        self.assertTrue(bw.iloc[:self.window-1].isna().all())
        
        # Check that subsequent values are not NaN
        self.assertFalse(qlo.iloc[self.window-1:].isna().any())
        self.assertFalse(qhi.iloc[self.window-1:].isna().any())
        self.assertFalse(mid.iloc[self.window-1:].isna().any())
        self.assertFalse(bw.iloc[self.window-1:].isna().any())
        
        # Check that Qlo < Mid < Qhi
        self.assertTrue((qlo.iloc[self.window-1:] < mid.iloc[self.window-1:]).all())
        self.assertTrue((mid.iloc[self.window-1:] < qhi.iloc[self.window-1:]).all())
        
        # Check that bandwidth is positive
        self.assertTrue((bw.iloc[self.window-1:] > 0).all())
    
    def test_rolling_box_bounds_invalid_method(self):
        """Test rolling_box_bounds function with invalid method."""
        with self.assertRaises(ValueError):
            rolling_box_bounds(
                self.prices, self.window, self.q_lo, self.q_hi, method="invalid_method"
            )
    
    # Note: Testing the t-digest method would require the tdigest library to be installed
    # and would be more complex, so we'll skip it for now.


if __name__ == "__main__":
    unittest.main()