"""
Unit tests for the box_scanner module.
"""

import unittest
import numpy as np
import pandas as pd
from src.box_scanner import (
    discretize_series,
    count_effective_roundtrips,
    penalty_terms,
    score_window,
    scan_symbol
)


class TestBoxScanner(unittest.TestCase):
    
    def setUp(self):
        """Set up test data."""
        # Generate sample data
        np.random.seed(42)
        n = 1000
        self.close = pd.Series(
            100 + np.cumsum(np.random.randn(n) * 0.1), 
            name="close"
        )
        
        # Create simple quantile bounds for testing
        self.qlo = pd.Series(95.0, index=self.close.index)
        self.qhi = pd.Series(105.0, index=self.close.index)
        
        # Test parameters
        self.fees_bps = 5.0
        self.slip_bps = 3.0
        self.min_hold = 3
        self.weights = {
            'alpha': 1.0,
            'beta': 2.0,
            'gamma': 1.0,
            'delta': 0.5,
            'eta': 0.5,
            'lambda': 0.2,
            'zeta': 1.0
        }
    
    def test_discretize_series(self):
        """Test discretization of price series."""
        # Create a series with known values
        close = pd.Series([90, 95, 100, 105, 110])
        qlo = pd.Series([92, 92, 92, 92, 92])
        qhi = pd.Series([108, 108, 108, 108, 108])
        
        states = discretize_series(close, qlo, qhi)
        
        # Check expected states
        expected_states = pd.Series(['Low', 'Low', 'Mid', 'High', 'High'])
        pd.testing.assert_series_equal(states, expected_states)
    
    def test_count_effective_roundtrips_simple(self):
        """Test counting effective roundtrips with simple data."""
        # Create a simple series with clear Low-Mid-High-Mid-Low pattern
        close = pd.Series([90, 95, 100, 105, 110, 105, 100, 95, 90])
        qlo = pd.Series([92, 92, 92, 92, 92, 92, 92, 92, 92])
        qhi = pd.Series([108, 108, 108, 108, 108, 108, 108, 108, 108])
        
        states = discretize_series(close, qlo, qhi)
        
        # With these parameters, we expect:
        # Low at index 0, Mid at indices 1-2, High at indices 3-4, 
        # Mid at indices 5-6, Low at indices 7-8
        # This gives us one roundtrip: Low->High->Low
        # But with min_hold=3, it might not count as effective
        
        X, X_star, trip_stats = count_effective_roundtrips(
            close, states, qlo, qhi, self.fees_bps, self.slip_bps, self.min_hold
        )
        
        # We should have at least one total roundtrip
        self.assertGreaterEqual(X, 1)
        
        # The effective roundtrips depend on the cost calculation
        # For now, we'll just check that the function runs without error
        self.assertIsInstance(X_star, int)
        self.assertIsInstance(trip_stats, list)
    
    def test_penalty_terms(self):
        """Test calculation of penalty terms."""
        # Create simple data
        close = pd.Series([90, 95, 100, 105, 110])
        qlo = pd.Series([92, 92, 92, 92, 92])
        qhi = pd.Series([108, 108, 108, 108, 108])
        
        penalties = penalty_terms(close, qlo, qhi)
        
        # Check that all expected keys are present
        expected_keys = ['p', 'b', 's', 'r']
        for key in expected_keys:
            self.assertIn(key, penalties)
            self.assertIsInstance(penalties[key], pd.Series)
    
    def test_score_window(self):
        """Test calculation of window score."""
        # Simple test values
        X_star = 5
        penalties = {
            'p': 0.1,
            'b': 0.2,
            's': 0.05,
            'r': -0.1
        }
        liquidity_ok = True
        cost_risk = 0.05
        
        score = score_window(
            X_star, penalties, self.weights, liquidity_ok, cost_risk
        )
        
        # Check that score is a float
        self.assertIsInstance(score, float)
        
        # Check that score is calculated correctly (roughly)
        expected_score = (
            self.weights['alpha'] * X_star -
            self.weights['beta'] * penalties['p'] -
            self.weights['gamma'] * penalties['b'] -
            self.weights['delta'] * penalties['s'] +
            self.weights['eta'] * penalties['r'] +
            self.weights['lambda'] * liquidity_ok -
            self.weights['zeta'] * cost_risk
        )
        self.assertAlmostEqual(score, expected_score, places=5)
    
    # Note: Testing scan_symbol would require more complex setup and mock data
    # We'll skip it for now to keep the tests simple.


if __name__ == "__main__":
    unittest.main()