"""
Unit tests for the backtest module.
"""

import unittest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from src.backtest import (
    walk_forward_backtest,
    purged_kfold_split,
    evaluate_backtest_performance
)


class TestBacktest(unittest.TestCase):
    
    def setUp(self):
        """Set up test data."""
        # Generate sample data
        np.random.seed(42)
        n = 1000
        dates = pd.date_range(
            start="2023-01-01", 
            periods=n, 
            freq="4H", 
            tz="UTC"
        )
        
        # Generate price data
        prices = 100 + np.cumsum(np.random.randn(n) * 0.1)
        
        self.data = pd.DataFrame({
            'open': prices,
            'high': prices + np.random.rand(n) * 2,
            'low': prices - np.random.rand(n) * 2,
            'close': prices,
            'volume': np.random.rand(n) * 1000
        }, index=dates)
        
        # Simple config for testing
        self.config = {
            'backtest': {
                'walk_forward': {
                    'train_days': 30,
                    'test_days': 10,
                    'embargo_days': 2
                }
            },
            'bar': {
                'timeframe': '4h',
                'lookbacks': [60, 120]
            },
            'quantiles': {
                'q_lo': 0.1,
                'q_hi': 0.9
            },
            'scoring': {
                'min_holding_bars': 3,
                'min_edge_return_bps': 8,
                'weights': {
                    'alpha': 1.0,
                    'beta': 2.0,
                    'gamma': 1.0,
                    'delta': 0.5,
                    'eta': 0.5,
                    'lambda': 0.2,
                    'zeta': 1.0
                }
            },
            'costs': {
                'taker_fee_bps': 5,
                'slippage_bps': 3,
                'funding_bps_per_day': 0
            }
        }
    
    def test_purged_kfold_split(self):
        """Test purged K-fold split function."""
        # Test with simple parameters
        n_splits = 3
        embargo_days = 1
        timeframe = "4h"
        
        splits = purged_kfold_split(
            self.data, n_splits, embargo_days, timeframe
        )
        
        # Check that we get the expected number of splits
        self.assertEqual(len(splits), n_splits)
        
        # Check that each split is a tuple of two arrays
        for train_idx, test_idx in splits:
            self.assertIsInstance(train_idx, np.ndarray)
            self.assertIsInstance(test_idx, np.ndarray)
            
            # Check that indices are valid
            if len(train_idx) > 0:
                self.assertTrue((train_idx >= 0).all())
                self.assertTrue((train_idx < len(self.data)).all())
            if len(test_idx) > 0:
                self.assertTrue((test_idx >= 0).all())
                self.assertTrue((test_idx < len(self.data)).all())
    
    def test_evaluate_backtest_performance(self):
        """Test evaluation of backtest performance."""
        # Test with empty results
        empty_results = pd.DataFrame()
        empty_performance = evaluate_backtest_performance(
            empty_results, self.config
        )
        
        # Check that we get expected keys
        expected_keys = [
            'total_signals', 'avg_score', 'sharpe_ratio', 
            'max_drawdown', 'win_rate'
        ]
        for key in expected_keys:
            self.assertIn(key, empty_performance)
        
        # Test with simple results
        simple_results = pd.DataFrame({
            'score': [1.0, 2.0, 3.0]
        })
        
        simple_performance = evaluate_backtest_performance(
            simple_results, self.config
        )
        
        # Check some values
        self.assertEqual(simple_performance['total_signals'], 3)
        self.assertEqual(simple_performance['avg_score'], 2.0)
        # Other values are placeholders, so we won't check them
        
    # Note: Testing walk_forward_backtest would require a mock scan function
    # and would be more complex, so we'll skip it for now.


if __name__ == "__main__":
    unittest.main()