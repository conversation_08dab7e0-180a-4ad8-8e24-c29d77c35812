# Quantization Finance - Box Scanner

This project aims to automatically scan cryptocurrency K-line data to identify "box" (range-bound) patterns, score and rank them based on their "tradability", and optionally output corresponding entry/exit signals for subsequent strategies.

## Features

- Rolling quantile-based definition of box boundaries (avoiding look-ahead bias)
- Discretization of price data to count roundtrips between High and Low states
- Scoring system with penalties for various factors (breakout frequency, bandwidth, liquidity, etc.)
- Walk-forward and purged/embargo time series cross-validation for robustness evaluation

## Project Structure

```
project/
  README.md
  requirements.txt
  config/
    default.yaml
  data/
    raw/    # Raw K-line data
    proc/   # Aligned/deduplicated/validated K-line data
  src/
    data_loader.py          # Data loading, validation, alignment, resampling
    quantile_online.py      # Online quantile algorithms (t-digest/GK or sliding window)
    box_scanner.py          # Discretization, roundtrip counting, penalties, scoring
    cost_models.py          # Fee, slippage, funding rate estimation
    liquidity.py            # Liquidity filtering
    backtest.py             # Walk-forward + purged K-fold backtesting
    metrics.py              # Performance and robustness metrics
    plotting.py             # Visualization
    report.py               # Summary reports (HTML/notebook)
    cli.py                  # Command-line interface
  notebooks/
    exploration.ipynb
  tests/
    test_quantile_online.py
    test_box_scanner.py
    test_backtest_split.py
  artifacts/
    reports/
    signals/
```

## Getting Started

1. Install dependencies: `pip install -r requirements.txt`
2. Prepare data: Place your K-line data in `data/raw/`
3. Run the scanner: `python -m src.cli scan --config config/default.yaml`
4. Generate reports: `python -m src.cli report --last-run`

## Configuration

See `config/default.yaml` for default configuration options.

## License

This project is licensed under the MIT License.