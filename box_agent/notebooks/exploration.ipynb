{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Box Pattern Scanner - Data Exploration\n", "\n", "This notebook provides an exploratory analysis of the K-line data and demonstrates the usage of the box pattern scanner."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import os\n", "\n", "# Add parent directory to path to import modules\n", "sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(''))))\n", "\n", "from src.data_loader import load_kline_data\n", "from src.quantile_online import rolling_box_bounds\n", "from src.box_scanner import discretize_series, count_effective_roundtrips\n", "from src.plotting import plot_box_bounds, plot_discretized_series"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Data\n", "\n", "Load the K-line data for a specific symbol."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data for ETHUSDT as an example\n", "data_file = \"../ETHUSDT_4h.json\"\n", "\n", "try:\n", "    if os.path.exists(data_file):\n", "        df = load_kline_data(data_file, \"4h\")\n", "        print(f\"Loaded {len(df)} bars of data for ETHUSDT\")\n", "        print(df.head())\n", "    else:\n", "        print(f\"Data file {data_file} not found. Please make sure the data file exists.\")\n", "        # Generate sample data for demonstration\n", "        np.random.seed(42)\n", "        n = 1000\n", "        dates = pd.date_range(start=\"2023-01-01\", periods=n, freq=\"4H\", tz=\"UTC\")\n", "        prices = 100 + np.cumsum(np.random.randn(n) * 0.1)\n", "        \n", "        df = pd.DataFrame({\n", "            'open': prices,\n", "            'high': prices + np.random.rand(n) * 2,\n", "            'low': prices - np.random.rand(n) * 2,\n", "            'close': prices,\n", "            'volume': np.random.rand(n) * 1000,\n", "            'quote_asset_volume': np.random.rand(n) * 10000,\n", "            'number_of_trades': np.random.randint(100, 1000, n),\n", "            'taker_buy_base_asset_volume': np.random.rand(n) * 500,\n", "            'taker_buy_quote_asset_volume': np.random.rand(n) * 5000\n", "        }, index=dates)\n", "        \n", "        print(f\"Generated {len(df)} bars of sample data\")\n", "        print(df.head())\n", "except UnicodeDecodeError as e:\n", "    print(f\"UnicodeDecodeError occurred: {e}\")\n", "    print(\"Trying to load data with UTF-8 encoding...\")\n", "    # Generate sample data for demonstration\n", "    np.random.seed(42)\n", "    n = 1000\n", "    dates = pd.date_range(start=\"2023-01-01\", periods=n, freq=\"4H\", tz=\"UTC\")\n", "    prices = 100 + np.cumsum(np.random.randn(n) * 0.1)\n", "    \n", "    df = pd.DataFrame({\n", "        'open': prices,\n", "        'high': prices + np.random.rand(n) * 2,\n", "        'low': prices - np.random.rand(n) * 2,\n", "        'close': prices,\n", "        'volume': np.random.rand(n) * 1000,\n", "        'quote_asset_volume': np.random.rand(n) * 10000,\n", "        'number_of_trades': np.random.randint(100, 1000, n),\n", "        'taker_buy_base_asset_volume': np.random.rand(n) * 500,\n", "        'taker_buy_quote_asset_volume': np.random.rand(n) * 5000\n", "    }, index=dates)\n", "    \n", "    print(f\"Generated {len(df)} bars of sample data\")\n", "    print(df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate Box Bounds\n", "\n", "Calculate the rolling box bounds using the quantile method."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate box bounds\n", "window = 100\n", "q_lo = 0.1\n", "q_hi = 0.9\n", "\n", "qlo, qhi, mid, bw = rolling_box_bounds(\n", "    df['close'], \n", "    window, \n", "    q_lo, \n", "    q_hi,\n", "    method=\"sliding_window\"\n", ")\n", "\n", "print(f\"Calculated box bounds with window={window}, q_lo={q_lo}, q_hi={q_hi}\")\n", "print(f\"Last 5 values of Qlo: {qlo.tail().values}\")\n", "print(f\"Last 5 values of Qhi: {qhi.tail().values}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot Box Bounds\n", "\n", "Visualize the price series with the calculated box bounds."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot box bounds\n", "fig = plot_box_bounds(df, qlo, qhi, mid, \"ETHUSDT Box Boundaries\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Discretize Price Series\n", "\n", "Discretize the price series into Low, Mid, and High states."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Discretize price series\n", "states = discretize_series(df['close'], qlo, qhi)\n", "\n", "print(f\"Discretized states:\")\n", "print(f\"Low: {(states == 'Low').sum()} periods\")\n", "print(f\"Mid: {(states == 'Mid').sum()} periods\")\n", "print(f\"High: {(states == 'High').sum()} periods\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot Discretized Series\n", "\n", "Visualize the discretized price series."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot discretized series\n", "fig = plot_discretized_series(df, states, \"ETHUSDT Discretized Price Series\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Count Effective Roundtrips\n", "\n", "Count the effective roundtrips between Low and High states."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Count effective roundtrips\n", "fees_bps = 5.0\n", "slip_bps = 3.0\n", "min_hold = 3\n", "\n", "<PERSON>, <PERSON>_star, trip_stats = count_effective_roundtrips(\n", "    df['close'], states, qlo, qhi,\n", "    fees_bps, slip_bps, min_hold\n", ")\n", "\n", "print(f\"Total roundtrips (X): {X}\")\n", "print(f\"Effective roundtrips (X*): {X_star}\")\n", "print(f\"Number of trip stats: {len(trip_stats)}\")\n", "\n", "if trip_stats:\n", "    print(\"\\nFirst few trip statistics:\")\n", "    for i, trip in enumerate(trip_stats[:3]):\n", "        print(f\"  Trip {i+1}: {trip.start_time} to {trip.end_time}, Return: {trip.theoretical_return:.2f} bps\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "This notebook has demonstrated the basic usage of the box pattern scanner components:\n", "1. Loading and exploring K-line data\n", "2. Calculating box boundaries using quantiles\n", "3. Discretizing the price series\n", "4. Counting effective roundtrips\n", "5. Visualizing the results\n", "\n", "For a full scan across multiple symbols and lookback periods, please use the command-line interface."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}