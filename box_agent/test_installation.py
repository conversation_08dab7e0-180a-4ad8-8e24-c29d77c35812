"""
Test script to verify installation and basic functionality of the box scanner.
"""

import sys
import os

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported."""
    try:
        from src.data_loader import load_kline_data
        from src.quantile_online import rolling_box_bounds
        from src.box_scanner import discretize_series, count_effective_roundtrips, scan_symbol
        from src.cost_models import estimate_transaction_cost
        from src.liquidity import check_liquidity
        from src.backtest import walk_forward_backtest
        from src.plotting import plot_box_bounds
        from src.report import generate_summary_report
        from src.cli import load_config
        print("✓ All modules imported successfully")
        return True
    except Exception as e:
        print(f"✗ Error importing modules: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality of key functions."""
    try:
        import pandas as pd
        import numpy as np
        
        # Create sample data
        np.random.seed(42)
        n = 1000
        dates = pd.date_range(start="2023-01-01", periods=n, freq="4H", tz="UTC")
        prices = 100 + np.cumsum(np.random.randn(n) * 0.1)
        
        df = pd.DataFrame({
            'open': prices,
            'high': prices + np.random.rand(n) * 2,
            'low': prices - np.random.rand(n) * 2,
            'close': prices,
            'volume': np.random.rand(n) * 1000,
            'quote_asset_volume': np.random.rand(n) * 10000,
            'number_of_trades': np.random.randint(100, 1000, n),
            'taker_buy_base_asset_volume': np.random.rand(n) * 500,
            'taker_buy_quote_asset_volume': np.random.rand(n) * 5000
        }, index=dates)
        
        # Test rolling box bounds
        from src.quantile_online import rolling_box_bounds
        qlo, qhi, mid, bw = rolling_box_bounds(df['close'], 100, 0.1, 0.9)
        print("✓ Rolling box bounds calculated successfully")
        
        # Test discretization
        from src.box_scanner import discretize_series
        states = discretize_series(df['close'], qlo, qhi)
        print("✓ Price series discretized successfully")
        
        # Test roundtrip counting
        from src.box_scanner import count_effective_roundtrips
        X, X_star, trip_stats = count_effective_roundtrips(
            df['close'], states, qlo, qhi, 5.0, 3.0, 3
        )
        print(f"✓ Roundtrips counted successfully (X={X}, X*={X_star})")
        
        return True
    except Exception as e:
        print(f"✗ Error in basic functionality test: {e}")
        return False


def test_config_loading():
    """Test loading configuration."""
    try:
        import yaml
        with open("config/default.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✓ Configuration loaded successfully")
        return True
    except Exception as e:
        print(f"✗ Error loading configuration: {e}")
        return False


def main():
    """Main test function."""
    print("Testing box scanner installation and basic functionality...")
    print("=" * 60)
    
    # Test imports
    if not test_imports():
        return False
    
    # Test basic functionality
    if not test_basic_functionality():
        return False
    
    # Test config loading
    if not test_config_loading():
        return False
    
    print("=" * 60)
    print("✓ All tests passed! Installation and basic functionality verified.")
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)